using GMCadiomChat.Core.Interfaces;
using System;
using System.Drawing;
using System.Media;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace GMCadiomChat.Desktop.WinForms.Services;

public class NotificationService : INotificationService
{
    private readonly Form? _mainForm;

    public NotificationService(Form? mainForm = null)
    {
        _mainForm = mainForm;
    }

    public Task ShowNotificationAsync(string title, string message, string? iconPath = null)
    {
        return Task.Run(() =>
        {
            if (_mainForm?.InvokeRequired == true)
            {
                _mainForm.Invoke(() => ShowNotificationInternal(title, message, iconPath));
            }
            else
            {
                ShowNotificationInternal(title, message, iconPath);
            }
        });
    }

    private void ShowNotificationInternal(string title, string message, string? iconPath)
    {
        // Use Windows Forms MessageBox for simple notifications
        // In a production app, you might want to use Windows Toast notifications
        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    public Task ShowBuzzNotificationAsync(string fromUsername)
    {
        return Task.Run(async () =>
        {
            if (_mainForm?.InvokeRequired == true)
            {
                _mainForm.Invoke(() =>
                {
                    // Show notification
                    MessageBox.Show($"{fromUsername} sent you a buzz!", "Buzz!", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);

                    // Shake the window and play sound (fire and forget)
                    _ = Task.Run(async () =>
                    {
                        await ShakeWindowAsync();
                        await PlaySoundAsync("buzz");
                    });
                });
            }
            else
            {
                // Show notification
                MessageBox.Show($"{fromUsername} sent you a buzz!", "Buzz!", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                
                // Shake the window
                await ShakeWindowAsync();
                
                // Play buzz sound
                await PlaySoundAsync("buzz");
            }
        });
    }

    public Task ShakeWindowAsync()
    {
        if (_mainForm == null) return Task.CompletedTask;

        return Task.Run(() =>
        {
            if (_mainForm.InvokeRequired)
            {
                _mainForm.Invoke(ShakeWindowInternal);
            }
            else
            {
                ShakeWindowInternal();
            }
        });
    }

    private void ShakeWindowInternal()
    {
        if (_mainForm == null) return;

        var originalLocation = _mainForm.Location;
        var random = new Random();

        // Shake the window for a short duration
        for (int i = 0; i < 10; i++)
        {
            var offsetX = random.Next(-10, 11);
            var offsetY = random.Next(-10, 11);
            
            _mainForm.Location = new Point(
                originalLocation.X + offsetX,
                originalLocation.Y + offsetY
            );

            Application.DoEvents();
            System.Threading.Thread.Sleep(50);
        }

        // Restore original position
        _mainForm.Location = originalLocation;
    }

    public Task PlaySoundAsync(string soundType)
    {
        return Task.Run(() =>
        {
            try
            {
                switch (soundType.ToLower())
                {
                    case "buzz":
                        SystemSounds.Exclamation.Play();
                        break;
                    case "message":
                        SystemSounds.Asterisk.Play();
                        break;
                    case "notification":
                    default:
                        SystemSounds.Beep.Play();
                        break;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - sound is not critical
                System.Diagnostics.Debug.WriteLine($"Failed to play sound: {ex.Message}");
            }
        });
    }
}
