using GMCadiomChat.Core.Entities;
using Microsoft.EntityFrameworkCore;

namespace GMCadiomChat.Core.Data;

public class ChatDbContext : DbContext
{
    public ChatDbContext(DbContextOptions<ChatDbContext> options) : base(options)
    {
    }

    public DbSet<Client> Clients { get; set; }
    public DbSet<ChatMessage> Messages { get; set; }
    public DbSet<TextMessage> TextMessages { get; set; }
    public DbSet<ImageMessage> ImageMessages { get; set; }
    public DbSet<AudioMessage> AudioMessages { get; set; }
    public DbSet<ScreenShareMessage> ScreenShareMessages { get; set; }
    public DbSet<BuzzMessage> BuzzMessages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Client entity
        modelBuilder.Entity<Client>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ConnectionId).HasMaxLength(100);
            entity.HasIndex(e => e.Username).IsUnique();
            entity.HasIndex(e => e.Email).IsUnique();
        });

        // Configure ChatMessage hierarchy using TPH (Table Per Hierarchy)
        modelBuilder.Entity<ChatMessage>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.MessageType)
                .IsRequired()
                .HasConversion<string>();

            entity.HasDiscriminator<string>("Discriminator")
                .HasValue<TextMessage>("Text")
                .HasValue<ImageMessage>("Image")
                .HasValue<AudioMessage>("Audio")
                .HasValue<ScreenShareMessage>("ScreenShare")
                .HasValue<BuzzMessage>("Buzz");

            // Configure relationships
            entity.HasOne(e => e.Sender)
                .WithMany(e => e.SentMessages)
                .HasForeignKey(e => e.SenderId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Receiver)
                .WithMany(e => e.ReceivedMessages)
                .HasForeignKey(e => e.ReceiverId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Configure TextMessage
        modelBuilder.Entity<TextMessage>(entity =>
        {
            entity.Property(e => e.Content).IsRequired().HasMaxLength(4000);
        });

        // Configure ImageMessage
        modelBuilder.Entity<ImageMessage>(entity =>
        {
            entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
            entity.Property(e => e.FileFormat).IsRequired().HasMaxLength(10);
            entity.Property(e => e.Caption).HasMaxLength(500);
        });

        // Configure AudioMessage
        modelBuilder.Entity<AudioMessage>(entity =>
        {
            entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
            entity.Property(e => e.FileFormat).IsRequired().HasMaxLength(10);
        });

        // Configure ScreenShareMessage
        modelBuilder.Entity<ScreenShareMessage>(entity =>
        {
            entity.Property(e => e.SessionId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
        });

        // Configure BuzzMessage
        modelBuilder.Entity<BuzzMessage>(entity =>
        {
            entity.Property(e => e.CustomMessage).HasMaxLength(100);
        });
    }
}
