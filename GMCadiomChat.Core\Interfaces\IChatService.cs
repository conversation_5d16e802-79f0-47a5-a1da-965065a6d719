using GMCadiomChat.Core.Entities;
using GMCadiomChat.Shared.DTOs;
using GMCadiomChat.Shared.Enums;

namespace GMCadiomChat.Core.Interfaces;

public interface IChatService
{
    // Client management
    Task<ClientDto?> LoginAsync(LoginRequest request);
    Task LogoutAsync(Guid clientId);
    Task<ClientDto?> GetClientAsync(Guid clientId);
    Task<ClientDto?> GetClientByUsernameAsync(string username);
    Task<IEnumerable<ClientDto>> GetOnlineClientsAsync();
    Task UpdateClientStatusAsync(Guid clientId, UserStatus status);
    Task UpdateClientConnectionAsync(Guid clientId, string connectionId);

    // Message management
    Task<MessageDto> SendMessageAsync(Guid senderId, SendMessageRequest request);
    Task<IEnumerable<MessageDto>> GetConversationAsync(Guid user1Id, Guid user2Id, int skip = 0, int take = 50);
    Task<IEnumerable<MessageDto>> GetUserMessagesAsync(Guid userId, int skip = 0, int take = 50);
    Task<IEnumerable<MessageDto>> GetUndeliveredMessagesAsync(Guid userId);
    Task MarkMessageAsDeliveredAsync(Guid messageId);
    Task MarkMessageAsReadAsync(Guid messageId);
    Task<int> GetUnreadCountAsync(Guid userId, Guid fromUserId);

    // Special message types
    Task<MessageDto> SendBuzzAsync(Guid senderId, Guid receiverId);
    Task<MessageDto> StartScreenShareAsync(Guid senderId, Guid receiverId, string? description = null);
    Task EndScreenShareAsync(Guid messageId);

    // Events
    event EventHandler<ClientDto>? ClientConnected;
    event EventHandler<ClientDto>? ClientDisconnected;
    event EventHandler<ClientDto>? ClientStatusChanged;
    event EventHandler<MessageDto>? MessageSent;
    event EventHandler<MessageDto>? MessageDelivered;
    event EventHandler<MessageDto>? MessageRead;
}

public interface INotificationService
{
    Task ShowNotificationAsync(string title, string message, string? iconPath = null);
    Task ShowBuzzNotificationAsync(string fromUsername);
    Task ShakeWindowAsync();
    Task PlaySoundAsync(string soundType);
}

public interface IFileService
{
    Task<string> SaveFileAsync(byte[] fileData, string fileName, string fileType);
    Task<byte[]> ReadFileAsync(string filePath);
    Task DeleteFileAsync(string filePath);
    Task<bool> FileExistsAsync(string filePath);
    string GetFileExtension(string fileName);
    long GetFileSize(string filePath);
    Task<string> SaveImageAsync(byte[] imageData, string fileName);
    Task<string> SaveAudioAsync(byte[] audioData, string fileName);
}
