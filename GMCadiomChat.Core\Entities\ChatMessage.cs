using GMCadiomChat.Shared.Enums;

namespace GMCadiomChat.Core.Entities;

public abstract class ChatMessage
{
    public Guid Id { get; set; }
    public Guid SenderId { get; set; }
    public Guid? ReceiverId { get; set; }
    public DateTime Timestamp { get; set; }
    public MessageType MessageType { get; set; }
    public bool IsDelivered { get; set; }
    public bool IsRead { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }

    // Navigation properties
    public virtual Client Sender { get; set; } = null!;
    public virtual Client? Receiver { get; set; }

    protected ChatMessage()
    {
        Id = Guid.NewGuid();
        Timestamp = DateTime.UtcNow;
        IsDelivered = false;
        IsRead = false;
    }

    public abstract string GetContent();
    public abstract ChatMessage Clone();
}

public class TextMessage : ChatMessage
{
    public string Content { get; set; } = string.Empty;

    public TextMessage()
    {
        MessageType = MessageType.Text;
    }

    public TextMessage(string content) : this()
    {
        Content = content;
    }

    public override string GetContent() => Content;

    public override ChatMessage Clone()
    {
        return new TextMessage(Content)
        {
            Id = Id,
            SenderId = SenderId,
            ReceiverId = ReceiverId,
            Timestamp = Timestamp,
            IsDelivered = IsDelivered,
            IsRead = IsRead,
            DeliveredAt = DeliveredAt,
            ReadAt = ReadAt
        };
    }
}

public class ImageMessage : ChatMessage
{
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileFormat { get; set; } = string.Empty;
    public string? Caption { get; set; }

    public ImageMessage()
    {
        MessageType = MessageType.Image;
    }

    public override string GetContent() => Caption ?? $"Image: {Path.GetFileName(FilePath)}";

    public override ChatMessage Clone()
    {
        return new ImageMessage
        {
            Id = Id,
            SenderId = SenderId,
            ReceiverId = ReceiverId,
            Timestamp = Timestamp,
            IsDelivered = IsDelivered,
            IsRead = IsRead,
            DeliveredAt = DeliveredAt,
            ReadAt = ReadAt,
            FilePath = FilePath,
            FileSize = FileSize,
            FileFormat = FileFormat,
            Caption = Caption
        };
    }
}

public class AudioMessage : ChatMessage
{
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileFormat { get; set; } = string.Empty;
    public int Duration { get; set; } // Duration in seconds

    public AudioMessage()
    {
        MessageType = MessageType.Audio;
    }

    public override string GetContent() => $"Audio message ({Duration}s)";

    public override ChatMessage Clone()
    {
        return new AudioMessage
        {
            Id = Id,
            SenderId = SenderId,
            ReceiverId = ReceiverId,
            Timestamp = Timestamp,
            IsDelivered = IsDelivered,
            IsRead = IsRead,
            DeliveredAt = DeliveredAt,
            ReadAt = ReadAt,
            FilePath = FilePath,
            FileSize = FileSize,
            FileFormat = FileFormat,
            Duration = Duration
        };
    }
}

public class ScreenShareMessage : ChatMessage
{
    public string SessionId { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime? EndedAt { get; set; }

    public ScreenShareMessage()
    {
        MessageType = MessageType.ScreenShare;
        SessionId = Guid.NewGuid().ToString();
        IsActive = true;
    }

    public override string GetContent() => Description ?? "Screen sharing session";

    public override ChatMessage Clone()
    {
        return new ScreenShareMessage
        {
            Id = Id,
            SenderId = SenderId,
            ReceiverId = ReceiverId,
            Timestamp = Timestamp,
            IsDelivered = IsDelivered,
            IsRead = IsRead,
            DeliveredAt = DeliveredAt,
            ReadAt = ReadAt,
            SessionId = SessionId,
            Description = Description,
            IsActive = IsActive,
            EndedAt = EndedAt
        };
    }
}

public class BuzzMessage : ChatMessage
{
    public string? CustomMessage { get; set; }

    public BuzzMessage()
    {
        MessageType = MessageType.Buzz;
    }

    public override string GetContent() => CustomMessage ?? "Buzz!";

    public override ChatMessage Clone()
    {
        return new BuzzMessage
        {
            Id = Id,
            SenderId = SenderId,
            ReceiverId = ReceiverId,
            Timestamp = Timestamp,
            IsDelivered = IsDelivered,
            IsRead = IsRead,
            DeliveredAt = DeliveredAt,
            ReadAt = ReadAt,
            CustomMessage = CustomMessage
        };
    }
}
