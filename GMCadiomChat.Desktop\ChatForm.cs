using GMCadiomChat.Desktop.WinForms.Controls;
using GMCadiomChat.Desktop.WinForms.ViewModels;
using System.ComponentModel;

namespace GMCadiomChat.Desktop.WinForms;

public partial class ChatForm : Form
{
    private readonly ChatViewModel _viewModel;
    private readonly List<UserListItem> _userListItems = new();
    private readonly List<MessageBubble> _messageBubbles = new();

    public ChatForm()
    {
        InitializeComponent();
        _viewModel = new ChatViewModel(this);
        SetupDataBinding();
        SetupEventHandlers();

        // Set initial focus
        usernameTextBox.Focus();
    }

    private void SetupDataBinding()
    {
        // Bind ViewModel properties to UI controls
        usernameTextBox.DataBindings.Add("Text", _viewModel, nameof(_viewModel.Username), false, DataSourceUpdateMode.OnPropertyChanged);
        statusLabel.DataBindings.Add("Text", _viewModel, nameof(_viewModel.ConnectionStatus), false, DataSourceUpdateMode.OnPropertyChanged);
        typingLabel.DataBindings.Add("Text", _viewModel, nameof(_viewModel.TypingIndicator), false, DataSourceUpdateMode.OnPropertyChanged);
        messageTextBox.DataBindings.Add("Text", _viewModel, nameof(_viewModel.MessageText), false, DataSourceUpdateMode.OnPropertyChanged);

        // Note: Button enabled states and usernameTextBox.Enabled are handled in ViewModel_PropertyChanged
        // to properly handle the inverted logic for usernameTextBox (enabled when NOT connected)

        // Subscribe to collection changes
        _viewModel.OnlineUsers.CollectionChanged += OnlineUsers_CollectionChanged;
        _viewModel.Messages.CollectionChanged += Messages_CollectionChanged;

        // Subscribe to property changes
        _viewModel.PropertyChanged += ViewModel_PropertyChanged;

        // Initialize UI state
        UpdateSelectedUserDisplay();
        connectButton.Text = _viewModel.IsConnected ? "Disconnect" : "Connect";
        usernameTextBox.Enabled = !_viewModel.IsConnected;
    }

    private void SetupEventHandlers()
    {
        connectButton.Click += ConnectButton_Click;
        sendButton.Click += SendButton_Click;
        buzzButton.Click += BuzzButton_Click;
        messageTextBox.KeyDown += MessageTextBox_KeyDown;
        messageTextBox.TextChanged += MessageTextBox_TextChanged;
    }

    private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(() => ViewModel_PropertyChanged(sender, e));
            return;
        }

        switch (e.PropertyName)
        {
            case nameof(_viewModel.IsConnected):
                connectButton.Text = _viewModel.IsConnected ? "Disconnect" : "Connect";
                usernameTextBox.Enabled = !_viewModel.IsConnected;
                break;
            case nameof(_viewModel.SelectedUser):
                UpdateSelectedUserDisplay();
                break;
        }
    }

    private void UpdateSelectedUserDisplay()
    {
        if (_viewModel.SelectedUser != null)
        {
            selectedUserLabel.Text = _viewModel.SelectedUser.DisplayName;
            selectedUserStatusLabel.Text = _viewModel.SelectedUser.LastSeenDisplay;
            buzzButton.Enabled = true;
            messageTextBox.Enabled = true;
            sendButton.Enabled = true;
        }
        else
        {
            selectedUserLabel.Text = "Select a user to start chatting";
            selectedUserStatusLabel.Text = "";
            buzzButton.Enabled = false;
            messageTextBox.Enabled = false;
            sendButton.Enabled = false;
        }
    }

    private void OnlineUsers_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(() => OnlineUsers_CollectionChanged(sender, e));
            return;
        }

        switch (e.Action)
        {
            case System.Collections.Specialized.NotifyCollectionChangedAction.Add:
                if (e.NewItems != null)
                {
                    foreach (ClientViewModel user in e.NewItems)
                    {
                        AddUserToList(user);
                    }
                }
                break;
            case System.Collections.Specialized.NotifyCollectionChangedAction.Remove:
                if (e.OldItems != null)
                {
                    foreach (ClientViewModel user in e.OldItems)
                    {
                        RemoveUserFromList(user);
                    }
                }
                break;
            case System.Collections.Specialized.NotifyCollectionChangedAction.Reset:
                ClearUserList();
                break;
        }
    }

    private void AddUserToList(ClientViewModel user)
    {
        var userItem = new UserListItem
        {
            Client = user,
            Dock = DockStyle.Top
        };

        userItem.UserSelected += (s, selectedUser) =>
        {
            _viewModel.SelectUser(selectedUser);
        };

        _userListItems.Add(userItem);
        userListContainer.Controls.Add(userItem);
        userItem.BringToFront();
    }

    private void RemoveUserFromList(ClientViewModel user)
    {
        var userItem = _userListItems.FirstOrDefault(item => item.Client?.Id == user.Id);
        if (userItem != null)
        {
            _userListItems.Remove(userItem);
            userListContainer.Controls.Remove(userItem);
            userItem.Dispose();
        }
    }

    private void ClearUserList()
    {
        foreach (var userItem in _userListItems)
        {
            userListContainer.Controls.Remove(userItem);
            userItem.Dispose();
        }
        _userListItems.Clear();
    }

    private void Messages_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(() => Messages_CollectionChanged(sender, e));
            return;
        }

        switch (e.Action)
        {
            case System.Collections.Specialized.NotifyCollectionChangedAction.Add:
                if (e.NewItems != null)
                {
                    foreach (MessageViewModel message in e.NewItems)
                    {
                        AddMessageToChat(message);
                    }
                }
                break;
            case System.Collections.Specialized.NotifyCollectionChangedAction.Reset:
                ClearMessages();
                break;
        }
    }

    private void AddMessageToChat(MessageViewModel message)
    {
        var messageBubble = new MessageBubble
        {
            Message = message,
            Dock = DockStyle.Top
        };

        // Align message based on sender
        if (message.IsFromCurrentUser)
        {
            messageBubble.Anchor = AnchorStyles.Top | AnchorStyles.Right;
        }
        else
        {
            messageBubble.Anchor = AnchorStyles.Top | AnchorStyles.Left;
        }

        _messageBubbles.Add(messageBubble);
        messagesPanel.Controls.Add(messageBubble);
        messageBubble.BringToFront();

        // Auto-scroll to bottom
        messagesPanel.ScrollControlIntoView(messageBubble);
    }

    private void ClearMessages()
    {
        foreach (var messageBubble in _messageBubbles)
        {
            messagesPanel.Controls.Remove(messageBubble);
            messageBubble.Dispose();
        }
        _messageBubbles.Clear();
    }

    private async void ConnectButton_Click(object? sender, EventArgs e)
    {
        if (_viewModel.IsConnected)
        {
            await _viewModel.DisconnectCommand.ExecuteAsync(null);
        }
        else
        {
            await _viewModel.ConnectCommand.ExecuteAsync(null);
        }
    }

    private async void SendButton_Click(object? sender, EventArgs e)
    {
        await _viewModel.SendMessageCommand.ExecuteAsync(null);
    }

    private async void BuzzButton_Click(object? sender, EventArgs e)
    {
        await _viewModel.SendBuzzCommand.ExecuteAsync(null);
    }

    private async void MessageTextBox_KeyDown(object? sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter && !e.Shift)
        {
            e.Handled = true;
            e.SuppressKeyPress = true;
            await _viewModel.SendMessageCommand.ExecuteAsync(null);
        }
    }

    private void MessageTextBox_TextChanged(object? sender, EventArgs e)
    {
        _viewModel.HandleMessageTextChanged(messageTextBox.Text);
    }

    protected override void OnFormClosed(FormClosedEventArgs e)
    {
        if (_viewModel.IsConnected)
        {
            _viewModel.DisconnectCommand.Execute(null);
        }
        base.OnFormClosed(e);
    }
}
