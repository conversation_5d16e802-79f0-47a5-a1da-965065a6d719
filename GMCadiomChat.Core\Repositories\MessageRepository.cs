using GMCadiomChat.Core.Data;
using GMCadiomChat.Core.Entities;
using GMCadiomChat.Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace GMCadiomChat.Core.Repositories;

public class MessageRepository : Repository<ChatMessage>, IMessageRepository
{
    public MessageRepository(ChatDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<ChatMessage>> GetConversationAsync(Guid user1Id, Guid user2Id, int skip = 0, int take = 50)
    {
        return await _dbSet
            .Include(m => m.Sender)
            .Include(m => m.Receiver)
            .Where(m => (m.SenderId == user1Id && m.ReceiverId == user2Id) ||
                       (m.SenderId == user2Id && m.ReceiverId == user1Id))
            .OrderByDescending(m => m.Timestamp)
            .Skip(skip)
            .Take(take)
            .ToListAsync();
    }

    public async Task<IEnumerable<ChatMessage>> GetUserMessagesAsync(Guid userId, int skip = 0, int take = 50)
    {
        return await _dbSet
            .Include(m => m.Sender)
            .Include(m => m.Receiver)
            .Where(m => m.SenderId == userId || m.ReceiverId == userId)
            .OrderByDescending(m => m.Timestamp)
            .Skip(skip)
            .Take(take)
            .ToListAsync();
    }

    public async Task<IEnumerable<ChatMessage>> GetUndeliveredMessagesAsync(Guid userId)
    {
        return await _dbSet
            .Include(m => m.Sender)
            .Include(m => m.Receiver)
            .Where(m => m.ReceiverId == userId && !m.IsDelivered)
            .OrderBy(m => m.Timestamp)
            .ToListAsync();
    }

    public async Task MarkAsDeliveredAsync(Guid messageId)
    {
        var message = await GetByIdAsync(messageId);
        if (message != null && !message.IsDelivered)
        {
            message.IsDelivered = true;
            message.DeliveredAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public async Task MarkAsReadAsync(Guid messageId)
    {
        var message = await GetByIdAsync(messageId);
        if (message != null && !message.IsRead)
        {
            message.IsRead = true;
            message.ReadAt = DateTime.UtcNow;
            if (!message.IsDelivered)
            {
                message.IsDelivered = true;
                message.DeliveredAt = DateTime.UtcNow;
            }
            await _context.SaveChangesAsync();
        }
    }

    public async Task<int> GetUnreadCountAsync(Guid userId, Guid fromUserId)
    {
        return await _dbSet
            .CountAsync(m => m.ReceiverId == userId && m.SenderId == fromUserId && !m.IsRead);
    }
}
