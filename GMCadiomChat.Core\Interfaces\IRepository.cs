using System.Linq.Expressions;

namespace GMCadiomChat.Core.Interfaces;

public interface IRepository<T> where T : class
{
    Task<T?> GetByIdAsync(Guid id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
    Task<T> AddAsync(T entity);
    Task<T> UpdateAsync(T entity);
    Task DeleteAsync(T entity);
    Task DeleteAsync(Guid id);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
}

public interface IClientRepository : IRepository<GMCadiomChat.Core.Entities.Client>
{
    Task<GMCadiomChat.Core.Entities.Client?> GetByUsernameAsync(string username);
    Task<GMCadiomChat.Core.Entities.Client?> GetByEmailAsync(string email);
    Task<GMCadiomChat.Core.Entities.Client?> GetByConnectionIdAsync(string connectionId);
    Task<IEnumerable<GMCadiomChat.Core.Entities.Client>> GetOnlineClientsAsync();
    Task UpdateConnectionIdAsync(Guid clientId, string connectionId);
    Task UpdateStatusAsync(Guid clientId, GMCadiomChat.Shared.Enums.UserStatus status);
}

public interface IMessageRepository : IRepository<GMCadiomChat.Core.Entities.ChatMessage>
{
    Task<IEnumerable<GMCadiomChat.Core.Entities.ChatMessage>> GetConversationAsync(Guid user1Id, Guid user2Id, int skip = 0, int take = 50);
    Task<IEnumerable<GMCadiomChat.Core.Entities.ChatMessage>> GetUserMessagesAsync(Guid userId, int skip = 0, int take = 50);
    Task<IEnumerable<GMCadiomChat.Core.Entities.ChatMessage>> GetUndeliveredMessagesAsync(Guid userId);
    Task MarkAsDeliveredAsync(Guid messageId);
    Task MarkAsReadAsync(Guid messageId);
    Task<int> GetUnreadCountAsync(Guid userId, Guid fromUserId);
}
