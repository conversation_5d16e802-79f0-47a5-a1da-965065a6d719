using GMCadiomChat.Shared.DTOs;

namespace GMCadiomChat.Shared.Interfaces;

/// <summary>
/// Interface defining the methods that can be called by the server on connected clients
/// </summary>
public interface IChatHub
{
    Task ReceiveMessage(MessageDto message);
    Task UserConnected(ClientDto client);
    Task UserDisconnected(ClientDto client);
    Task UserStatusChanged(ClientDto client);
    Task ReceiveBuzz(Guid fromUserId, string fromUsername);
    Task ReceiveTypingIndicator(Guid userId, string username, bool isTyping);
    Task MessageDelivered(Guid messageId);
    Task MessageRead(Guid messageId);
}

/// <summary>
/// Interface defining the methods that can be called by clients on the server
/// </summary>
public interface IChatServer
{
    Task SendMessage(SendMessageRequest request);
    Task SendBuzz(Guid toUserId);
    Task JoinChat(LoginRequest request);
    Task LeaveChat();
    Task SetTyping(Guid? toUserId, bool isTyping);
    Task MarkMessageAsRead(Guid messageId);
    Task GetOnlineUsers();
    Task GetMessageHistory(Guid? withUserId, int skip = 0, int take = 50);
}
