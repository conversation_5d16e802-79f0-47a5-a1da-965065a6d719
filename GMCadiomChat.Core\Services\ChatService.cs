using GMCadiomChat.Core.Entities;
using GMCadiomChat.Core.Interfaces;
using GMCadiomChat.Shared.DTOs;
using GMCadiomChat.Shared.Enums;

namespace GMCadiomChat.Core.Services;

public class ChatService : IChatService
{
    private readonly IClientRepository _clientRepository;
    private readonly IMessageRepository _messageRepository;

    public ChatService(IClientRepository clientRepository, IMessageRepository messageRepository)
    {
        _clientRepository = clientRepository;
        _messageRepository = messageRepository;
    }

    public event EventHandler<ClientDto>? ClientConnected;
    public event EventHandler<ClientDto>? ClientDisconnected;
    public event EventHandler<ClientDto>? ClientStatusChanged;
    public event EventHandler<MessageDto>? MessageSent;
    public event EventHandler<MessageDto>? MessageDelivered;
    public event EventHandler<MessageDto>? MessageRead;

    public async Task<ClientDto?> LoginAsync(LoginRequest request)
    {
        // Check if client already exists
        var existingClient = await _clientRepository.GetByUsernameAsync(request.Username);
        if (existingClient != null)
        {
            // Update status to online
            existingClient.Status = UserStatus.Online;
            existingClient.LastSeen = DateTime.UtcNow;
            await _clientRepository.UpdateAsync(existingClient);
            
            var clientDto = MapToDto(existingClient);
            ClientConnected?.Invoke(this, clientDto);
            return clientDto;
        }

        // Create new client
        var newClient = new Client
        {
            Username = request.Username,
            Email = request.Email,
            Status = UserStatus.Online
        };

        await _clientRepository.AddAsync(newClient);
        var newClientDto = MapToDto(newClient);
        ClientConnected?.Invoke(this, newClientDto);
        return newClientDto;
    }

    public async Task LogoutAsync(Guid clientId)
    {
        var client = await _clientRepository.GetByIdAsync(clientId);
        if (client != null)
        {
            client.Status = UserStatus.Offline;
            client.LastSeen = DateTime.UtcNow;
            client.ConnectionId = string.Empty;
            await _clientRepository.UpdateAsync(client);
            
            ClientDisconnected?.Invoke(this, MapToDto(client));
        }
    }

    public async Task<ClientDto?> GetClientAsync(Guid clientId)
    {
        var client = await _clientRepository.GetByIdAsync(clientId);
        return client != null ? MapToDto(client) : null;
    }

    public async Task<ClientDto?> GetClientByUsernameAsync(string username)
    {
        var client = await _clientRepository.GetByUsernameAsync(username);
        return client != null ? MapToDto(client) : null;
    }

    public async Task<IEnumerable<ClientDto>> GetOnlineClientsAsync()
    {
        var clients = await _clientRepository.GetOnlineClientsAsync();
        return clients.Select(MapToDto);
    }

    public async Task UpdateClientStatusAsync(Guid clientId, UserStatus status)
    {
        await _clientRepository.UpdateStatusAsync(clientId, status);
        var client = await _clientRepository.GetByIdAsync(clientId);
        if (client != null)
        {
            ClientStatusChanged?.Invoke(this, MapToDto(client));
        }
    }

    public async Task UpdateClientConnectionAsync(Guid clientId, string connectionId)
    {
        await _clientRepository.UpdateConnectionIdAsync(clientId, connectionId);
    }

    public async Task<MessageDto> SendMessageAsync(Guid senderId, SendMessageRequest request)
    {
        ChatMessage message = request.MessageType switch
        {
            MessageType.Text => new TextMessage(request.Content),
            MessageType.Image => new ImageMessage
            {
                FilePath = request.FilePath ?? string.Empty,
                FileSize = request.FileSize ?? 0,
                FileFormat = request.FileFormat ?? string.Empty,
                Caption = request.Content
            },
            MessageType.Audio => new AudioMessage
            {
                FilePath = request.FilePath ?? string.Empty,
                FileSize = request.FileSize ?? 0,
                FileFormat = request.FileFormat ?? string.Empty,
                Duration = request.Duration ?? 0
            },
            _ => new TextMessage(request.Content)
        };

        message.SenderId = senderId;
        message.ReceiverId = request.ReceiverId;

        await _messageRepository.AddAsync(message);
        
        var messageDto = await MapToMessageDto(message);
        MessageSent?.Invoke(this, messageDto);
        return messageDto;
    }

    public async Task<IEnumerable<MessageDto>> GetConversationAsync(Guid user1Id, Guid user2Id, int skip = 0, int take = 50)
    {
        var messages = await _messageRepository.GetConversationAsync(user1Id, user2Id, skip, take);
        var messageDtos = new List<MessageDto>();
        
        foreach (var message in messages)
        {
            messageDtos.Add(await MapToMessageDto(message));
        }
        
        return messageDtos;
    }

    public async Task<IEnumerable<MessageDto>> GetUserMessagesAsync(Guid userId, int skip = 0, int take = 50)
    {
        var messages = await _messageRepository.GetUserMessagesAsync(userId, skip, take);
        var messageDtos = new List<MessageDto>();
        
        foreach (var message in messages)
        {
            messageDtos.Add(await MapToMessageDto(message));
        }
        
        return messageDtos;
    }

    public async Task<IEnumerable<MessageDto>> GetUndeliveredMessagesAsync(Guid userId)
    {
        var messages = await _messageRepository.GetUndeliveredMessagesAsync(userId);
        var messageDtos = new List<MessageDto>();
        
        foreach (var message in messages)
        {
            messageDtos.Add(await MapToMessageDto(message));
        }
        
        return messageDtos;
    }

    public async Task MarkMessageAsDeliveredAsync(Guid messageId)
    {
        await _messageRepository.MarkAsDeliveredAsync(messageId);
        var message = await _messageRepository.GetByIdAsync(messageId);
        if (message != null)
        {
            MessageDelivered?.Invoke(this, await MapToMessageDto(message));
        }
    }

    public async Task MarkMessageAsReadAsync(Guid messageId)
    {
        await _messageRepository.MarkAsReadAsync(messageId);
        var message = await _messageRepository.GetByIdAsync(messageId);
        if (message != null)
        {
            MessageRead?.Invoke(this, await MapToMessageDto(message));
        }
    }

    public async Task<int> GetUnreadCountAsync(Guid userId, Guid fromUserId)
    {
        return await _messageRepository.GetUnreadCountAsync(userId, fromUserId);
    }

    public async Task<MessageDto> SendBuzzAsync(Guid senderId, Guid receiverId)
    {
        var buzzMessage = new BuzzMessage
        {
            SenderId = senderId,
            ReceiverId = receiverId
        };

        await _messageRepository.AddAsync(buzzMessage);
        
        var messageDto = await MapToMessageDto(buzzMessage);
        MessageSent?.Invoke(this, messageDto);
        return messageDto;
    }

    public async Task<MessageDto> StartScreenShareAsync(Guid senderId, Guid receiverId, string? description = null)
    {
        var screenShareMessage = new ScreenShareMessage
        {
            SenderId = senderId,
            ReceiverId = receiverId,
            Description = description
        };

        await _messageRepository.AddAsync(screenShareMessage);
        
        var messageDto = await MapToMessageDto(screenShareMessage);
        MessageSent?.Invoke(this, messageDto);
        return messageDto;
    }

    public async Task EndScreenShareAsync(Guid messageId)
    {
        var message = await _messageRepository.GetByIdAsync(messageId);
        if (message is ScreenShareMessage screenShareMessage)
        {
            screenShareMessage.IsActive = false;
            screenShareMessage.EndedAt = DateTime.UtcNow;
            await _messageRepository.UpdateAsync(screenShareMessage);
        }
    }

    private static ClientDto MapToDto(Client client)
    {
        return new ClientDto
        {
            Id = client.Id,
            Username = client.Username,
            Email = client.Email,
            Status = client.Status,
            LastSeen = client.LastSeen,
            ConnectionId = client.ConnectionId
        };
    }

    private async Task<MessageDto> MapToMessageDto(ChatMessage message)
    {
        var sender = message.Sender ?? await _clientRepository.GetByIdAsync(message.SenderId);
        var receiver = message.Receiver != null ? message.Receiver : 
                      (message.ReceiverId.HasValue ? await _clientRepository.GetByIdAsync(message.ReceiverId.Value) : null);

        var dto = new MessageDto
        {
            Id = message.Id,
            SenderId = message.SenderId,
            SenderUsername = sender?.Username ?? "Unknown",
            ReceiverId = message.ReceiverId,
            ReceiverUsername = receiver?.Username,
            Timestamp = message.Timestamp,
            MessageType = message.MessageType,
            Content = message.GetContent(),
            IsDelivered = message.IsDelivered,
            IsRead = message.IsRead
        };

        // Set type-specific properties
        switch (message)
        {
            case ImageMessage img:
                dto.FilePath = img.FilePath;
                dto.FileSize = img.FileSize;
                dto.FileFormat = img.FileFormat;
                break;
            case AudioMessage audio:
                dto.FilePath = audio.FilePath;
                dto.FileSize = audio.FileSize;
                dto.FileFormat = audio.FileFormat;
                dto.Duration = audio.Duration;
                break;
        }

        return dto;
    }
}
