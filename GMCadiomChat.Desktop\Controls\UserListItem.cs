using GMCadiomChat.Desktop.WinForms.ViewModels;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace GMCadiomChat.Desktop.WinForms.Controls;

public class UserListItem : UserControl
{
    private ClientViewModel? _client;
    private Label _nameLabel = null!;
    private Label _statusLabel = null!;
    private Label _unreadLabel = null!;
    private Panel _statusIndicator = null!;

    public ClientViewModel? Client
    {
        get => _client;
        set
        {
            if (_client != null)
            {
                _client.PropertyChanged -= OnClientPropertyChanged;
            }
            
            _client = value;
            
            if (_client != null)
            {
                _client.PropertyChanged += OnClientPropertyChanged;
            }
            
            UpdateDisplay();
        }
    }

    public event EventHandler<ClientViewModel>? UserSelected;

    public UserListItem()
    {
        InitializeComponent();
        SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
    }

    private void InitializeComponent()
    {
        SuspendLayout();

        // Status indicator (colored circle)
        _statusIndicator = new Panel
        {
            Size = new Size(12, 12),
            Location = new Point(12, 12)
        };

        // Name label
        _nameLabel = new Label
        {
            AutoSize = false,
            Font = new Font("Segoe UI", 9F, FontStyle.Regular),
            ForeColor = Color.FromArgb(33, 33, 33),
            Location = new Point(32, 8),
            Size = new Size(150, 20)
        };

        // Status label
        _statusLabel = new Label
        {
            AutoSize = false,
            Font = new Font("Segoe UI", 8F, FontStyle.Regular),
            ForeColor = Color.FromArgb(117, 117, 117),
            Location = new Point(32, 28),
            Size = new Size(150, 16)
        };

        // Unread count label
        _unreadLabel = new Label
        {
            AutoSize = false,
            Font = new Font("Segoe UI", 8F, FontStyle.Bold),
            ForeColor = Color.White,
            BackColor = Color.FromArgb(255, 64, 129),
            TextAlign = ContentAlignment.MiddleCenter,
            Size = new Size(20, 20),
            Location = new Point(200, 12),
            Visible = false
        };

        Controls.Add(_statusIndicator);
        Controls.Add(_nameLabel);
        Controls.Add(_statusLabel);
        Controls.Add(_unreadLabel);

        Size = new Size(240, 48);
        Cursor = Cursors.Hand;
        Padding = new Padding(8);

        Click += OnClick;
        _nameLabel.Click += OnClick;
        _statusLabel.Click += OnClick;

        ResumeLayout(false);
    }

    private void OnClientPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(() => UpdateDisplay());
        }
        else
        {
            UpdateDisplay();
        }
    }

    private void UpdateDisplay()
    {
        if (_client == null) return;

        _nameLabel.Text = _client.DisplayName;
        _statusLabel.Text = _client.StatusDisplay;
        
        // Update status indicator color
        _statusIndicator.BackColor = _client.StatusColorValue;
        
        // Update unread count
        if (_client.HasUnreadMessages)
        {
            _unreadLabel.Text = _client.UnreadCountDisplay;
            _unreadLabel.Visible = true;
        }
        else
        {
            _unreadLabel.Visible = false;
        }

        // Update selection state
        BackColor = _client.IsSelected ? Color.FromArgb(245, 245, 245) : Color.White;

        Invalidate();
    }

    private void OnClick(object? sender, EventArgs e)
    {
        if (_client != null)
        {
            UserSelected?.Invoke(this, _client);
        }
    }

    protected override void OnPaint(PaintEventArgs e)
    {
        base.OnPaint(e);

        // Draw status indicator as circle
        if (_statusIndicator.BackColor != Color.Empty)
        {
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            using (var brush = new SolidBrush(_statusIndicator.BackColor))
            {
                var rect = new Rectangle(_statusIndicator.Location, _statusIndicator.Size);
                e.Graphics.FillEllipse(brush, rect);
            }
        }

        // Draw unread count background as circle
        if (_unreadLabel.Visible)
        {
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            using (var brush = new SolidBrush(_unreadLabel.BackColor))
            {
                var rect = new Rectangle(_unreadLabel.Location, _unreadLabel.Size);
                e.Graphics.FillEllipse(brush, rect);
            }
        }
    }

    protected override void OnMouseEnter(EventArgs e)
    {
        base.OnMouseEnter(e);
        if (_client != null && !_client.IsSelected)
        {
            BackColor = Color.FromArgb(250, 250, 250);
        }
    }

    protected override void OnMouseLeave(EventArgs e)
    {
        base.OnMouseLeave(e);
        if (_client != null && !_client.IsSelected)
        {
            BackColor = Color.White;
        }
    }
}
