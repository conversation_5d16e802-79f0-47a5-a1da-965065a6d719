using GMCadiomChat.Shared.Enums;

namespace GMCadiomChat.Shared.DTOs;

public class MessageDto
{
    public Guid Id { get; set; }
    public Guid SenderId { get; set; }
    public string SenderUsername { get; set; } = string.Empty;
    public Guid? ReceiverId { get; set; }
    public string? ReceiverUsername { get; set; }
    public DateTime Timestamp { get; set; }
    public MessageType MessageType { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? FilePath { get; set; }
    public long? FileSize { get; set; }
    public string? FileFormat { get; set; }
    public int? Duration { get; set; } // For audio messages in seconds
    public bool IsDelivered { get; set; }
    public bool IsRead { get; set; }
}

public class SendMessageRequest
{
    public Guid? ReceiverId { get; set; }
    public MessageType MessageType { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? FilePath { get; set; }
    public long? FileSize { get; set; }
    public string? FileFormat { get; set; }
    public int? Duration { get; set; }
}

public class LoginRequest
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
}

public class LoginResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public ClientDto? Client { get; set; }
}
