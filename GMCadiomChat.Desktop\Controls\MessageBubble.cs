using GMCadiomChat.Desktop.WinForms.ViewModels;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace GMCadiomChat.Desktop.WinForms.Controls;

public class MessageBubble : UserControl
{
    private MessageViewModel? _message;
    private Label _contentLabel = null!;
    private Label _timeLabel = null!;
    private Label _statusLabel = null!;

    public MessageViewModel? Message
    {
        get => _message;
        set
        {
            _message = value;
            UpdateDisplay();
        }
    }

    public MessageBubble()
    {
        InitializeComponent();
        SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
    }

    private void InitializeComponent()
    {
        SuspendLayout();

        // Content label
        _contentLabel = new Label
        {
            AutoSize = false,
            TextAlign = ContentAlignment.TopLeft,
            Font = new Font("Segoe UI", 9F, FontStyle.Regular),
            Padding = new Padding(0),
            Margin = new Padding(0)
        };

        // Time label
        _timeLabel = new Label
        {
            AutoSize = true,
            Font = new Font("Segoe UI", 7F, FontStyle.Regular),
            ForeColor = Color.Gray,
            TextAlign = ContentAlignment.BottomRight
        };

        // Status label
        _statusLabel = new Label
        {
            AutoSize = true,
            Font = new Font("Segoe UI", 7F, FontStyle.Regular),
            ForeColor = Color.Gray,
            TextAlign = ContentAlignment.BottomRight
        };

        Controls.Add(_contentLabel);
        Controls.Add(_timeLabel);
        Controls.Add(_statusLabel);

        AutoSize = false;
        Margin = new Padding(5);
        Padding = new Padding(12, 8, 12, 8);

        ResumeLayout(false);
    }

    private void UpdateDisplay()
    {
        if (_message == null) return;

        _contentLabel.Text = _message.GetDisplayContent();
        _timeLabel.Text = _message.DisplayTime;
        _statusLabel.Text = _message.StatusIcon;

        // Set colors based on message sender
        if (_message.IsFromCurrentUser)
        {
            BackColor = Color.FromArgb(33, 150, 243); // Blue
            _contentLabel.ForeColor = Color.White;
            _timeLabel.ForeColor = Color.FromArgb(200, 255, 255, 255);
            _statusLabel.ForeColor = Color.FromArgb(200, 255, 255, 255);
        }
        else
        {
            BackColor = Color.FromArgb(224, 224, 224); // Light gray
            _contentLabel.ForeColor = Color.FromArgb(33, 33, 33);
            _timeLabel.ForeColor = Color.FromArgb(117, 117, 117);
            _statusLabel.ForeColor = Color.FromArgb(117, 117, 117);
        }

        LayoutControls();
        Invalidate();
    }

    private void LayoutControls()
    {
        if (_message == null) return;

        var contentSize = TextRenderer.MeasureText(_contentLabel.Text, _contentLabel.Font, new Size(300, 0), TextFormatFlags.WordBreak);
        var timeSize = TextRenderer.MeasureText(_timeLabel.Text, _timeLabel.Font);
        var statusSize = TextRenderer.MeasureText(_statusLabel.Text, _statusLabel.Font);

        var bubbleWidth = Math.Max(contentSize.Width, timeSize.Width + statusSize.Width + 10) + Padding.Horizontal;
        var bubbleHeight = contentSize.Height + timeSize.Height + 5 + Padding.Vertical;

        Size = new Size(bubbleWidth, bubbleHeight);

        _contentLabel.Location = new Point(Padding.Left, Padding.Top);
        _contentLabel.Size = new Size(bubbleWidth - Padding.Horizontal, contentSize.Height);

        _timeLabel.Location = new Point(Padding.Left, bubbleHeight - Padding.Bottom - timeSize.Height);
        _statusLabel.Location = new Point(bubbleWidth - Padding.Right - statusSize.Width, bubbleHeight - Padding.Bottom - statusSize.Height);
    }

    protected override void OnPaint(PaintEventArgs e)
    {
        e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

        // Draw rounded rectangle background
        using (var path = GetRoundedRectanglePath(ClientRectangle, 12))
        {
            using (var brush = new SolidBrush(BackColor))
            {
                e.Graphics.FillPath(brush, path);
            }
        }

        base.OnPaint(e);
    }

    private GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
    {
        var path = new GraphicsPath();
        var diameter = radius * 2;

        path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
        path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
        path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
        path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
        path.CloseFigure();

        return path;
    }

    protected override void OnResize(EventArgs e)
    {
        base.OnResize(e);
        Invalidate();
    }
}
