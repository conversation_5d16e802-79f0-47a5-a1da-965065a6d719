using GMCadiomChat.Core.Data;
using GMCadiomChat.Core.Entities;
using GMCadiomChat.Core.Interfaces;
using GMCadiomChat.Shared.Enums;
using Microsoft.EntityFrameworkCore;

namespace GMCadiomChat.Core.Repositories;

public class ClientRepository : Repository<Client>, IClientRepository
{
    public ClientRepository(ChatDbContext context) : base(context)
    {
    }

    public async Task<Client?> GetByUsernameAsync(string username)
    {
        return await _dbSet.FirstOrDefaultAsync(c => c.Username == username);
    }

    public async Task<Client?> GetByEmailAsync(string email)
    {
        return await _dbSet.FirstOrDefaultAsync(c => c.Email == email);
    }

    public async Task<Client?> GetByConnectionIdAsync(string connectionId)
    {
        return await _dbSet.FirstOrDefaultAsync(c => c.ConnectionId == connectionId);
    }

    public async Task<IEnumerable<Client>> GetOnlineClientsAsync()
    {
        return await _dbSet
            .Where(c => c.Status == UserStatus.Online)
            .OrderBy(c => c.Username)
            .ToListAsync();
    }

    public async Task UpdateConnectionIdAsync(Guid clientId, string connectionId)
    {
        var client = await GetByIdAsync(clientId);
        if (client != null)
        {
            client.ConnectionId = connectionId;
            client.LastSeen = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public async Task UpdateStatusAsync(Guid clientId, UserStatus status)
    {
        var client = await GetByIdAsync(clientId);
        if (client != null)
        {
            client.Status = status;
            client.LastSeen = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }
}
