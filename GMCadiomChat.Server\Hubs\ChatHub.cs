using GMCadiomChat.Core.Interfaces;
using GMCadiomChat.Shared.DTOs;
using GMCadiomChat.Shared.Interfaces;
using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;

namespace GMCadiomChat.Server.Hubs;

public class ChatHub : Hub<IChatHub>, IChatServer
{
    private readonly IChatService _chatService;
    private static readonly ConcurrentDictionary<string, Guid> _connectionUserMap = new();
    private static readonly ConcurrentDictionary<Guid, string> _userConnectionMap = new();

    public ChatHub(IChatService chatService)
    {
        _chatService = chatService;
    }

    public async Task JoinChat(LoginRequest request)
    {
        try
        {
            var client = await _chatService.LoginAsync(request);
            if (client != null)
            {
                // Map connection to user
                _connectionUserMap[Context.ConnectionId] = client.Id;
                _userConnectionMap[client.Id] = Context.ConnectionId;

                // Update connection ID in database
                await _chatService.UpdateClientConnectionAsync(client.Id, Context.ConnectionId);

                // Add to SignalR group for the user
                await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{client.Id}");

                // Notify all clients about the new user
                await Clients.All.UserConnected(client);

                // Send undelivered messages to the user
                var undeliveredMessages = await _chatService.GetUndeliveredMessagesAsync(client.Id);
                foreach (var message in undeliveredMessages)
                {
                    await Clients.Caller.ReceiveMessage(message);
                    await _chatService.MarkMessageAsDeliveredAsync(message.Id);
                }

                // Send current online users to the new client
                var onlineUsers = await _chatService.GetOnlineClientsAsync();
                foreach (var user in onlineUsers.Where(u => u.Id != client.Id))
                {
                    await Clients.Caller.UserConnected(user);
                }
            }
        }
        catch (Exception ex)
        {
            await Clients.Caller.ReceiveMessage(new MessageDto
            {
                Id = Guid.NewGuid(),
                Content = $"Login failed: {ex.Message}",
                Timestamp = DateTime.UtcNow,
                MessageType = GMCadiomChat.Shared.Enums.MessageType.Text
            });
        }
    }

    public async Task LeaveChat()
    {
        if (_connectionUserMap.TryGetValue(Context.ConnectionId, out var userId))
        {
            await _chatService.LogoutAsync(userId);
            
            _connectionUserMap.TryRemove(Context.ConnectionId, out _);
            _userConnectionMap.TryRemove(userId, out _);

            var client = await _chatService.GetClientAsync(userId);
            if (client != null)
            {
                await Clients.All.UserDisconnected(client);
            }
        }
    }

    public async Task SendMessage(SendMessageRequest request)
    {
        if (_connectionUserMap.TryGetValue(Context.ConnectionId, out var senderId))
        {
            try
            {
                var message = await _chatService.SendMessageAsync(senderId, request);

                // Send to specific user if specified, otherwise broadcast to all
                if (request.ReceiverId.HasValue)
                {
                    // Send to specific receiver
                    if (_userConnectionMap.TryGetValue(request.ReceiverId.Value, out var receiverConnectionId))
                    {
                        await Clients.Client(receiverConnectionId).ReceiveMessage(message);
                        await _chatService.MarkMessageAsDeliveredAsync(message.Id);
                    }
                    // Also send back to sender for confirmation
                    await Clients.Caller.ReceiveMessage(message);
                }
                else
                {
                    // Broadcast to all connected clients
                    await Clients.All.ReceiveMessage(message);
                }
            }
            catch (Exception ex)
            {
                await Clients.Caller.ReceiveMessage(new MessageDto
                {
                    Id = Guid.NewGuid(),
                    Content = $"Failed to send message: {ex.Message}",
                    Timestamp = DateTime.UtcNow,
                    MessageType = GMCadiomChat.Shared.Enums.MessageType.Text
                });
            }
        }
    }

    public async Task SendBuzz(Guid toUserId)
    {
        if (_connectionUserMap.TryGetValue(Context.ConnectionId, out var senderId))
        {
            try
            {
                var sender = await _chatService.GetClientAsync(senderId);
                if (sender != null)
                {
                    var buzzMessage = await _chatService.SendBuzzAsync(senderId, toUserId);

                    // Send buzz to specific user
                    if (_userConnectionMap.TryGetValue(toUserId, out var receiverConnectionId))
                    {
                        await Clients.Client(receiverConnectionId).ReceiveBuzz(senderId, sender.Username);
                        await Clients.Client(receiverConnectionId).ReceiveMessage(buzzMessage);
                        await _chatService.MarkMessageAsDeliveredAsync(buzzMessage.Id);
                    }

                    // Send confirmation back to sender
                    await Clients.Caller.ReceiveMessage(buzzMessage);
                }
            }
            catch (Exception ex)
            {
                await Clients.Caller.ReceiveMessage(new MessageDto
                {
                    Id = Guid.NewGuid(),
                    Content = $"Failed to send buzz: {ex.Message}",
                    Timestamp = DateTime.UtcNow,
                    MessageType = GMCadiomChat.Shared.Enums.MessageType.Text
                });
            }
        }
    }

    public async Task SetTyping(Guid? toUserId, bool isTyping)
    {
        if (_connectionUserMap.TryGetValue(Context.ConnectionId, out var senderId))
        {
            var sender = await _chatService.GetClientAsync(senderId);
            if (sender != null)
            {
                if (toUserId.HasValue && _userConnectionMap.TryGetValue(toUserId.Value, out var receiverConnectionId))
                {
                    // Send typing indicator to specific user
                    await Clients.Client(receiverConnectionId).ReceiveTypingIndicator(senderId, sender.Username, isTyping);
                }
                else
                {
                    // Broadcast typing indicator to all users
                    await Clients.Others.ReceiveTypingIndicator(senderId, sender.Username, isTyping);
                }
            }
        }
    }

    public async Task MarkMessageAsRead(Guid messageId)
    {
        if (_connectionUserMap.TryGetValue(Context.ConnectionId, out var userId))
        {
            await _chatService.MarkMessageAsReadAsync(messageId);
            
            // Notify sender that message was read
            var message = await _chatService.GetUserMessagesAsync(userId, 0, 1);
            var readMessage = message.FirstOrDefault(m => m.Id == messageId);
            if (readMessage != null && _userConnectionMap.TryGetValue(readMessage.SenderId, out var senderConnectionId))
            {
                await Clients.Client(senderConnectionId).MessageRead(messageId);
            }
        }
    }

    public async Task GetOnlineUsers()
    {
        var onlineUsers = await _chatService.GetOnlineClientsAsync();
        foreach (var user in onlineUsers)
        {
            await Clients.Caller.UserConnected(user);
        }
    }

    public async Task GetMessageHistory(Guid? withUserId, int skip = 0, int take = 50)
    {
        if (_connectionUserMap.TryGetValue(Context.ConnectionId, out var userId))
        {
            IEnumerable<MessageDto> messages;
            
            if (withUserId.HasValue)
            {
                messages = await _chatService.GetConversationAsync(userId, withUserId.Value, skip, take);
            }
            else
            {
                messages = await _chatService.GetUserMessagesAsync(userId, skip, take);
            }

            foreach (var message in messages.Reverse()) // Send in chronological order
            {
                await Clients.Caller.ReceiveMessage(message);
            }
        }
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        if (_connectionUserMap.TryGetValue(Context.ConnectionId, out var userId))
        {
            await _chatService.LogoutAsync(userId);
            
            _connectionUserMap.TryRemove(Context.ConnectionId, out _);
            _userConnectionMap.TryRemove(userId, out _);

            var client = await _chatService.GetClientAsync(userId);
            if (client != null)
            {
                await Clients.All.UserDisconnected(client);
            }
        }

        await base.OnDisconnectedAsync(exception);
    }
}
