using GMCadiomChat.Core.Interfaces;
using GMCadiomChat.Shared.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace GMCadiomChat.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ChatController : ControllerBase
{
    private readonly IChatService _chatService;

    public ChatController(IChatService chatService)
    {
        _chatService = chatService;
    }

    [HttpGet("online-users")]
    public async Task<ActionResult<IEnumerable<ClientDto>>> GetOnlineUsers()
    {
        var users = await _chatService.GetOnlineClientsAsync();
        return Ok(users);
    }

    [HttpGet("user/{userId}/messages")]
    public async Task<ActionResult<IEnumerable<MessageDto>>> GetUserMessages(
        Guid userId, 
        [FromQuery] int skip = 0, 
        [FromQuery] int take = 50)
    {
        var messages = await _chatService.GetUserMessagesAsync(userId, skip, take);
        return Ok(messages);
    }

    [HttpGet("conversation/{user1Id}/{user2Id}")]
    public async Task<ActionResult<IEnumerable<MessageDto>>> GetConversation(
        Guid user1Id, 
        Guid user2Id, 
        [FromQuery] int skip = 0, 
        [FromQuery] int take = 50)
    {
        var messages = await _chatService.GetConversationAsync(user1Id, user2Id, skip, take);
        return Ok(messages);
    }

    [HttpPost("message/{messageId}/mark-read")]
    public async Task<ActionResult> MarkMessageAsRead(Guid messageId)
    {
        await _chatService.MarkMessageAsReadAsync(messageId);
        return Ok();
    }

    [HttpGet("user/{userId}/unread-count/{fromUserId}")]
    public async Task<ActionResult<int>> GetUnreadCount(Guid userId, Guid fromUserId)
    {
        var count = await _chatService.GetUnreadCountAsync(userId, fromUserId);
        return Ok(count);
    }
}
